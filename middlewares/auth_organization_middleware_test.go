package middlewares

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg/config"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestAuthMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that checks context values
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - valid API key with organization stored in context", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		expectedOrgID := appioid.MustParse("org_00000000000000000000000001")
		expectedSvcID := appioid.MustParse("svc_00000000000000000000000001")
		testAPIKey := "test_api_key_12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			GetDataByAPIKey(gomock.Any(), testAPIKey).
			Return(expectedOrgID, expectedSvcID, nil).
			Times(1)

		// Handler that checks organization ID is in context
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			orgID, ok := GetOrganizationIDFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, expectedOrgID, orgID)
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("success"))
		})

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+testAPIKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid API key with matching service ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		expectedOrgID := appioid.MustParse("org_00000000000000000000000001")
		expectedSvcID := appioid.MustParse("svc_00000000000000000000000001")
		testAPIKey := "test_api_key_12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			GetDataByAPIKey(gomock.Any(), testAPIKey).
			Return(expectedOrgID, expectedSvcID, nil).
			Times(1)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+testAPIKey)
		// Add service ID to context (simulating HeaderToContextMiddleware)
		ctx := context.WithValue(req.Context(), SvcIDKey{}, expectedSvcID)
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - missing Authorization header", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("Error - empty Bearer token", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("Error - invalid API key format", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer short-key") // Too short
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("Error - GetDataByAPIKey returns error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		testAPIKey := "test_api_key_12345678901234567890123456789012345678901234567890"
		expectedError := errors.New("database connection failed")

		mockRepo.EXPECT().
			GetDataByAPIKey(gomock.Any(), testAPIKey).
			Return(nil, nil, expectedError).
			Times(1)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+testAPIKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
		assert.Contains(t, w.Body.String(), "error")
	})

	t.Run("Error - service ID mismatch returns 403", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		expectedOrgID := appioid.MustParse("org_00000000000000000000000001")
		dbServiceID := appioid.MustParse("svc_00000000000000000000000001")
		contextServiceID := appioid.MustParse("svc_00000000000000000000000002") // Different service
		testAPIKey := "test_api_key_12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			GetDataByAPIKey(gomock.Any(), testAPIKey).
			Return(expectedOrgID, dbServiceID, nil).
			Times(1)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+testAPIKey)
		// Add different service ID to context
		ctx := context.WithValue(req.Context(), SvcIDKey{}, contextServiceID)
		req = req.WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
		assert.Contains(t, w.Body.String(), "error")

		// Verify the error response structure
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Success - no service ID in context (some routes don't use X-Service-Id)", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		authConfig := &config.AuthConfig{App: "app-key", IOS: "ios-key", Demo: "demo-key"}
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		expectedOrgID := appioid.MustParse("org_00000000000000000000000001")
		expectedSvcID := appioid.MustParse("svc_00000000000000000000000001")
		testAPIKey := "test_api_key_12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			GetDataByAPIKey(gomock.Any(), testAPIKey).
			Return(expectedOrgID, expectedSvcID, nil).
			Times(1)

		middleware := AuthMiddleware(apiKeyService, logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+testAPIKey)
		// No service ID in context - should still pass
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})
}
