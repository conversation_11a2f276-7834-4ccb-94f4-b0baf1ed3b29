#!/bin/bash

# Generate mocks for all repository interfaces

echo "Generating mocks..."

cd ..

# Create mocks directory if it doesn't exist
mkdir -p internal/mocks

# Generate mocks for each repository interface
go run go.uber.org/mock/mockgen -source=repositories/apikey_repository.go -destination=internal/mocks/mock_apikey_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/device_repository.go -destination=internal/mocks/mock_device_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/device_name_repository.go -destination=internal/mocks/mock_device_name_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/deviceservice_repository.go -destination=internal/mocks/mock_deviceservice_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/featureflag_repository.go -destination=internal/mocks/mock_featureflag_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/feedback_repository.go -destination=internal/mocks/mock_feedback_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/fingerprint_repository.go -destination=internal/mocks/mock_fingerprint_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/notification_repository.go -destination=internal/mocks/mock_notification_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/notificationdelivery_repository.go -destination=internal/mocks/mock_notificationdelivery_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/service_repository.go -destination=internal/mocks/mock_service_repository.go -package=mocks
go run go.uber.org/mock/mockgen -source=repositories/widget_repository.go -destination=internal/mocks/mock_widget_repository.go -package=mocks

# Generate mocks for each service interface
go run go.uber.org/mock/mockgen -source=services/device_service.go -destination=internal/mocks/mock_device_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/featureflag_service.go -destination=internal/mocks/mock_featureflag_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/feedback_service.go -destination=internal/mocks/mock_feedback_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/fingerprint_service.go -destination=internal/mocks/mock_fingerprint_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/jwt_service.go -destination=internal/mocks/mock_jwt_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/lastseenat_service.go -destination=internal/mocks/mock_lastseenat_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/notification_service.go -destination=internal/mocks/mock_notification_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/service_service.go -destination=internal/mocks/mock_service_service.go -package=mocks
go run go.uber.org/mock/mockgen -source=services/widget_service.go -destination=internal/mocks/mock_widget_service.go -package=mocks

echo "Mocks generated successfully!"
