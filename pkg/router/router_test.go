package router

import (
	"bytes"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
)

// Helper function to create a complete service container with mocks for testing
func createTestServiceContainer(t *testing.T) (*services.ServiceContainer, *gomock.Controller) {
	ctrl := gomock.NewController(t)

	// Create a mock API key service that will validate our test tokens
	mockAPIKeyRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
	authConfig := &config.AuthConfig{
		App:     "app-key_12345678901234567890123456789012345678901234567890",
		IOS:     "ios-key_12345678901234567890123456789012345678901234567890",
		Android: "android-key_12345678901234567890123456789012345678901234567890",
		Demo:    "demo-key_12345678901234567890123456789012345678901234567890",
	}
	logger := zap.NewNop()

	// Set up expectations for API key lookups
	// For the specific API key used in tests, return true (active)
	mockAPIKeyRepo.EXPECT().
		IsActiveAPIKey(gomock.Any(), "api-key_12345678901234567890123456789012345678901234567890").
		Return(true).
		AnyTimes()

	// For unknown keys, return false (inactive)
	mockAPIKeyRepo.EXPECT().
		IsActiveAPIKey(gomock.Any(), gomock.Any()).
		Return(false).
		AnyTimes()

	apiKeyService := services.NewAPIKeyService(mockAPIKeyRepo, authConfig, logger)

	// Create minimal services that won't panic for testing middleware
	// We're testing middleware, not service logic, so these can be minimal
	mockFeatureFlagRepo := mocks.NewMockFeatureFlagRepositoryInterface(ctrl)
	mockFeatureFlagRepo.EXPECT().
		GetBy(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, nil).
		AnyTimes()

	featureFlagService := services.NewFeatureFlagService(mockFeatureFlagRepo, nil, logger)

	// Create mock repositories for DeviceService
	mockDeviceRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	mockDeviceServiceRepo := mocks.NewMockDeviceServiceRepositoryInterface(ctrl)
	mockDeviceNameRepo := mocks.NewMockDeviceNameRepositoryInterface(ctrl)

	deviceService := services.NewDeviceService(mockDeviceRepo, mockDeviceServiceRepo, mockDeviceNameRepo, nil, logger)

	// Create LastSeenAtService with mock repository
	// Set up expectations for LastSeenAtMiddleware
	mockDeviceRepo.EXPECT().
		UpdateLastSeenAt(gomock.Any(), gomock.Any()).
		Return(nil).
		AnyTimes()

	lastSeenAtService := services.NewLastSeenAtService(mockDeviceRepo, logger)

	// Create mock repositories for ServiceService
	mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
	mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)

	// Set up expectations for ServiceService methods that might be called
	mockServiceRepo.EXPECT().
		FindByID(gomock.Any(), gomock.Any()).
		Return(nil, nil).
		AnyTimes()

	mockWidgetRepo.EXPECT().
		List(gomock.Any(), gomock.Any()).
		Return(nil, nil).
		AnyTimes()

	// Create WidgetConfigService first since ServiceService depends on it
	widgetConfigService := services.NewWidgetConfigService(logger)

	serviceService := services.NewServiceService(mockServiceRepo, mockWidgetRepo, widgetConfigService, logger)

	// For other services, we'll create minimal instances
	// Since we're testing middleware, these shouldn't be called in our tests
	notificationService := &services.NotificationService{}
	widgetService := &services.WidgetService{}
	fingerprintService := &services.FingerprintService{}
	feedbackService := &services.FeedbackService{}
	jwtService := &services.JWTService{}

	return &services.ServiceContainer{
		APIKeyService:       apiKeyService,
		FeatureFlagService:  featureFlagService,
		ServiceService:      serviceService,
		DeviceService:       deviceService,
		NotificationService: notificationService,
		WidgetService:       widgetService,
		WidgetConfigService: widgetConfigService,
		FingerprintService:  fingerprintService,
		FeedbackService:     feedbackService,
		JWTService:          jwtService,
		LastSeenAtService:   lastSeenAtService,
	}, ctrl
}

// Helper function to create test config
func createTestConfig(t *testing.T, serviceContainer *services.ServiceContainer) Config {
	logger := zaptest.NewLogger(t)

	return Config{
		Logger: logger,
		DB:     &pgxpool.Pool{}, // Empty pool for testing
		DBFing: &pgxpool.Pool{}, // Empty pool for testing
		Config: &config.Config{
			Server: config.ServerConfig{
				Timeout: "30s",
				Env:     "test",
			},
		},
		Services:          serviceContainer,
		RateLimitRequests: 100,
		RateLimitWindow:   60,
		RateLimitBurst:    10,
	}
}

func TestNewRouter(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)

	t.Run("Creates router successfully", func(t *testing.T) {
		router := NewRouter(cfg)
		assert.NotNil(t, router)
		assert.IsType(t, &chi.Mux{}, router)
	})

	t.Run("Health check endpoint works", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "🟢")
	})

	t.Run("Index endpoint redirects", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusFound, w.Code)
		assert.Equal(t, "https://docs.appio.so/", w.Header().Get("Location"))
	})

	t.Run("Robots.txt endpoint works", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "User-agent: *")
		assert.Contains(t, w.Body.String(), "Disallow: /")
	})

	t.Run("Unknown routes require authentication first", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/nonexistent-endpoint", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// All routes except public ones go through auth middleware first
		// So unknown routes return 401 (Unauthorized) not 404 (Not Found)
		assert.Equal(t, http.StatusUnauthorized, w.Code)
		// Content-Type might be set by error handler, not global middleware
		contentType := w.Header().Get("Content-Type")
		assert.Contains(t, contentType, "application/json")
	})

	t.Run("Method not allowed on public routes requires auth first", func(t *testing.T) {
		router := NewRouter(cfg)

		// Even POST to /health-check goes through auth middleware because
		// the router structure has auth middleware on the "/" route group
		req := httptest.NewRequest("POST", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// This actually returns 401 because auth middleware runs first
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Method not allowed on protected routes requires auth first", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("POST", "/mobile/ff", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Auth middleware runs before method checking for protected routes
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestRouterGlobalMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)

	t.Run("Content-Type header is set", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// The health check handler calls helpers.RenderJSON which sets "application/json"
		// This overrides the global middleware's "application/json; charset=utf-8"
		contentType := w.Header().Get("Content-Type")
		assert.Equal(t, "application/json", contentType)
	})

	t.Run("X-Content-Type-Options header is set", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	})

	t.Run("Rate limiting is bypassed in test environment", func(t *testing.T) {
		// Test environment should bypass rate limiting
		router := NewRouter(cfg)

		// Make multiple requests quickly
		for i := 0; i < 5; i++ {
			req := httptest.NewRequest("GET", "/health-check", nil)
			w := httptest.NewRecorder()

			router.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		}
	})

	t.Run("Timeout middleware is applied", func(t *testing.T) {
		// Create config with very short timeout
		shortTimeoutCfg := cfg
		shortTimeoutCfg.Config.Server.Timeout = "1ms"

		router := NewRouter(shortTimeoutCfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Should still work for fast requests like health check
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Invalid timeout configuration", func(t *testing.T) {
		invalidTimeoutCfg := cfg
		invalidTimeoutCfg.Config.Server.Timeout = "invalid-timeout"

		router := NewRouter(invalidTimeoutCfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})
}

// Helper function to create authenticated request
func createAuthenticatedRequest(method, url string, body []byte, role roles.Role, svcID *appioid.ID) *http.Request {
	var req *http.Request
	if body != nil {
		req = httptest.NewRequest(method, url, bytes.NewBuffer(body))
		req.Header.Set("Content-Type", "application/json")
	} else {
		req = httptest.NewRequest(method, url, nil)
	}

	// Set Authorization header with the appropriate API key for the role
	var apiKey string
	switch role {
	case roles.IOS:
		apiKey = "ios-key_12345678901234567890123456789012345678901234567890"
	case roles.Android:
		apiKey = "android-key_12345678901234567890123456789012345678901234567890"
	case roles.Api:
		// For API role, we need a key that will be looked up in the database
		// Since we're not mocking the database, this will return Unknown role
		apiKey = "api-key_12345678901234567890123456789012345678901234567890"
	case roles.ApiDemo:
		// Demo API keys have a special format: demo_<prefix>_<26-char-service-id>
		if svcID != nil && svcID.Type() == "demo_svc" {
			// Extract the 26-char ID from the service ID
			svcIDStr := svcID.String()
			if len(svcIDStr) >= 26 {
				apiKey = "demo_prefix_" + svcIDStr[len(svcIDStr)-26:]
			} else {
				apiKey = "demo_prefix_00000000000000000000000000"
			}
		} else {
			apiKey = "demo_prefix_00000000000000000000000000"
		}
	case roles.AppAppioSo:
		apiKey = "app-key_12345678901234567890123456789012345678901234567890"
	case roles.DemoAppioSo:
		apiKey = "demo-key_12345678901234567890123456789012345678901234567890"
	default:
		apiKey = "unknown-key_12345678901234567890123456789012345678901234567890"
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)

	return req
}

func TestRouterAuthentication(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)

	t.Run("Protected routes require authentication", func(t *testing.T) {
		router := NewRouter(cfg)

		protectedRoutes := []struct {
			method string
			path   string
		}{
			{"GET", "/mobile/ff"},
			{"GET", "/v1/services"},
			{"GET", "/demo-appio-so/services/demo_svc_00000000000000000000000001"},
			{"GET", "/app-appio-so/services/svc_00000000000000000000000001"},
			{"GET", "/hi"},
		}

		for _, route := range protectedRoutes {
			t.Run(fmt.Sprintf("%s %s", route.method, route.path), func(t *testing.T) {
				req := httptest.NewRequest(route.method, route.path, nil)
				w := httptest.NewRecorder()

				router.ServeHTTP(w, req)

				assert.Equal(t, http.StatusUnauthorized, w.Code,
					"Route %s %s should require authentication", route.method, route.path)
			})
		}
	})

	t.Run("Invalid Authorization header format", func(t *testing.T) {
		router := NewRouter(cfg)

		testCases := []struct {
			name   string
			header string
		}{
			{"Missing Bearer prefix", "test-api-key"},
			{"Empty Bearer token", "Bearer "},
			{"Only Bearer", "Bearer"},
			{"Invalid format", "Basic dGVzdA=="},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				req := httptest.NewRequest("GET", "/hi", nil)
				req.Header.Set("Authorization", tc.header)
				w := httptest.NewRecorder()

				router.ServeHTTP(w, req)

				assert.Equal(t, http.StatusUnauthorized, w.Code)
			})
		}
	})
}

func TestRouterConfiguration(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	t.Run("Different rate limit configurations", func(t *testing.T) {
		testCases := []struct {
			name     string
			requests int
			window   int
			burst    int
		}{
			{"Low limits", 10, 60, 5},
			{"High limits", 1000, 3600, 100},
			{"Minimal limits", 1, 1, 1}, // Avoid zero division
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				cfg := createTestConfig(t, serviceContainer)
				cfg.RateLimitRequests = tc.requests
				cfg.RateLimitWindow = tc.window
				cfg.RateLimitBurst = tc.burst

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})

	t.Run("Different environments", func(t *testing.T) {
		environments := []string{"development", "staging", "production", "test"}

		for _, env := range environments {
			t.Run("Environment: "+env, func(t *testing.T) {
				cfg := createTestConfig(t, serviceContainer)
				cfg.Config.Server.Env = env

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})

	t.Run("Different timeout configurations", func(t *testing.T) {
		timeouts := []int{10, 30, 60, 120}

		for _, timeout := range timeouts {
			t.Run("Timeout", func(t *testing.T) {
				cfg := createTestConfig(t, serviceContainer)
				cfg.Config.Server.Timeout = fmt.Sprintf("%ds", timeout)

				router := NewRouter(cfg)
				assert.NotNil(t, router)
			})
		}
	})
}

func TestMobileRouteMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("Mobile routes require iOS or Android role", func(t *testing.T) {
		testCases := []struct {
			name          string
			role          roles.Role
			expectSuccess bool
		}{
			{"iOS role allowed", roles.IOS, true},
			{"Android role allowed", roles.Android, true},
			{"API role denied", roles.Api, false},
			{"Demo role denied", roles.DemoAppioSo, false},
			{"Unknown role denied", roles.Unknown, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse("svc_00000000000000000000000001")
				req := createAuthenticatedRequest("GET", "/mobile/ff", nil, tc.role, svcID)
				req.Header.Set("X-App-Version", "1.0.0")
				req.Header.Set("X-Service-Id", svcID.String())
				req.Header.Set("X-App-Platform", "ios")

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectSuccess {
					// Should not be forbidden (might be other errors like missing handlers)
					assert.NotEqual(t, http.StatusForbidden, w.Code)
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
				}
			})
		}
	})

	t.Run("Mobile routes parse headers correctly", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		testCases := []struct {
			name        string
			headers     map[string]string
			expectError bool
		}{
			{
				name: "Valid headers",
				headers: map[string]string{
					"X-Service-Id":   svcID.String(),
					"X-Device-Id":    dvcID.String(),
					"X-App-Platform": "ios",
				},
				expectError: false,
			},
			{
				name: "Invalid Service ID",
				headers: map[string]string{
					"X-Service-Id":   "invalid-id",
					"X-Device-Id":    dvcID.String(),
					"X-App-Platform": "ios",
				},
				expectError: true,
			},
			{
				name: "Invalid Device ID",
				headers: map[string]string{
					"X-Service-Id":   svcID.String(),
					"X-Device-Id":    "invalid-id",
					"X-App-Platform": "ios",
				},
				expectError: true,
			},
			{
				name: "Invalid Platform",
				headers: map[string]string{
					"X-Service-Id":   svcID.String(),
					"X-Device-Id":    dvcID.String(),
					"X-App-Platform": "invalid-platform",
				},
				expectError: true,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				req := createAuthenticatedRequest("GET", "/mobile/ff", nil, roles.IOS, svcID)
				req.Header.Set("X-App-Version", "1.0.0")

				for key, value := range tc.headers {
					req.Header.Set(key, value)
				}

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectError {
					assert.Equal(t, http.StatusBadRequest, w.Code)
				} else {
					// Should not be a bad request (might be other errors)
					assert.NotEqual(t, http.StatusBadRequest, w.Code)
				}
			})
		}
	})

	t.Run("Mobile routes require platform header", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		// Missing platform header should cause 400 error since FeatureFlagHandler requires platform
		req := createAuthenticatedRequest("GET", "/mobile/ff", nil, roles.IOS, svcID)
		req.Header.Set("X-App-Version", "1.0.0")
		// Not setting X-App-Platform header

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should be a bad request since platform is required by the handler
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestConfigStruct(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	t.Run("Config struct fields", func(t *testing.T) {
		cfg := createTestConfig(t, serviceContainer)

		assert.NotNil(t, cfg.Logger)
		assert.NotNil(t, cfg.DB)
		assert.NotNil(t, cfg.DBFing)
		assert.NotNil(t, cfg.Config)
		assert.NotNil(t, cfg.Services)
		assert.Equal(t, 100, cfg.RateLimitRequests)
		assert.Equal(t, 60, cfg.RateLimitWindow)
		assert.Equal(t, 10, cfg.RateLimitBurst)
	})

	t.Run("Config with nil values", func(t *testing.T) {
		cfg := Config{
			Logger:            nil,
			DB:                nil,
			DBFing:            nil,
			Config:            nil,
			Services:          nil,
			RateLimitRequests: 0,
			RateLimitWindow:   0,
			RateLimitBurst:    0,
		}

		// Should panic when creating router with nil values
		// because the router tries to access config fields
		assert.Panics(t, func() {
			NewRouter(cfg)
		})
	})
}

func TestDemoRouteMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("Demo routes require DemoAppioSo role", func(t *testing.T) {
		testCases := []struct {
			name          string
			role          roles.Role
			expectSuccess bool
		}{
			{"Demo role allowed", roles.DemoAppioSo, true},
			{"API role denied", roles.Api, false},
			{"iOS role denied", roles.IOS, false},
			{"Android role denied", roles.Android, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				demoSvcID := appioid.MustParse("demo_svc_00000000000000000000000001")
				req := createAuthenticatedRequest("GET", "/demo-appio-so/services/"+demoSvcID.String(), nil, tc.role, demoSvcID)
				req.Header.Set("X-Service-Id", demoSvcID.String())

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectSuccess {
					assert.NotEqual(t, http.StatusForbidden, w.Code)
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
				}
			})
		}
	})

	t.Run("Demo routes validate service ID prefix", func(t *testing.T) {
		testCases := []struct {
			name        string
			serviceID   string
			expectError bool
		}{
			{"Valid demo service ID", "demo_svc_00000000000000000000000001", false},
			{"Invalid regular service ID", "svc_00000000000000000000000001", true},
			{"Invalid API service ID", "api_svc_00000000000000000000000001", true},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse(tc.serviceID)
				req := createAuthenticatedRequest("GET", "/demo-appio-so/services/"+tc.serviceID, nil, roles.DemoAppioSo, svcID)
				req.Header.Set("X-Service-Id", tc.serviceID)

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectError {
					assert.Equal(t, http.StatusBadRequest, w.Code)
				} else {
					assert.NotEqual(t, http.StatusBadRequest, w.Code)
				}
			})
		}
	})
}

func TestV1RouteMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("V1 routes require API roles", func(t *testing.T) {
		testCases := []struct {
			name          string
			role          roles.Role
			expectSuccess bool
		}{
			{"API role allowed", roles.Api, true},
			{"API Demo role allowed", roles.ApiDemo, true},
			{"Dashboard role denied (not implemented via API keys)", roles.Dashboard, false},
			{"iOS role denied", roles.IOS, false},
			{"Android role denied", roles.Android, false},
			{"Demo role denied", roles.DemoAppioSo, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse("svc_00000000000000000000000001")
				req := createAuthenticatedRequest("GET", "/v1/services", nil, tc.role, svcID)

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectSuccess {
					assert.NotEqual(t, http.StatusForbidden, w.Code)
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
				}
			})
		}
	})
}

func TestAppAppioSoRouteMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("App.appio.so routes require AppAppioSo role", func(t *testing.T) {
		testCases := []struct {
			name          string
			role          roles.Role
			expectSuccess bool
		}{
			{"AppAppioSo role allowed", roles.AppAppioSo, true},
			{"API role denied", roles.Api, false},
			{"iOS role denied", roles.IOS, false},
			{"Demo role denied", roles.DemoAppioSo, false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse("svc_00000000000000000000000001")
				req := createAuthenticatedRequest("GET", "/app-appio-so/services/"+svcID.String(), nil, tc.role, svcID)
				req.Header.Set("X-Service-Id", svcID.String())

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				if tc.expectSuccess {
					assert.NotEqual(t, http.StatusForbidden, w.Code)
				} else {
					assert.Equal(t, http.StatusForbidden, w.Code)
				}
			})
		}
	})
}

func TestHiRouteMiddlewares(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("Hi route accepts multiple roles", func(t *testing.T) {
		allowedRoles := []roles.Role{
			roles.Api, roles.ApiDemo, roles.AppAppioSo,
			roles.DemoAppioSo, roles.IOS, roles.Android,
		}

		for _, role := range allowedRoles {
			t.Run("Role: "+string(role), func(t *testing.T) {
				svcID := appioid.MustParse("svc_00000000000000000000000001")
				req := createAuthenticatedRequest("GET", "/hi", nil, role, svcID)

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				assert.NotEqual(t, http.StatusForbidden, w.Code)
				// Should return OK with "👋" message
				if w.Code == http.StatusOK {
					assert.Contains(t, w.Body.String(), "👋")
				}
			})
		}
	})

	t.Run("Hi route denies unknown role", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		req := createAuthenticatedRequest("GET", "/hi", nil, roles.Unknown, svcID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}

func TestRouterErrorScenarios(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)

	t.Run("Router handles panic recovery", func(t *testing.T) {
		router := NewRouter(cfg)

		// Test that the recoverer middleware is working
		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Should not panic and should return a response
		assert.NotEqual(t, 0, w.Code)
	})

	t.Run("Router handles CORS preflight", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("OPTIONS", "/hi", nil)
		req.Header.Set("Origin", "https://example.com")
		req.Header.Set("Access-Control-Request-Method", "GET")
		req.Header.Set("Authorization", "Bearer test-api-key")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// CORS middleware should handle OPTIONS requests
		assert.Contains(t, []int{http.StatusOK, http.StatusNoContent, http.StatusUnauthorized}, w.Code)
	})

	t.Run("Router handles malformed JSON in request body", func(t *testing.T) {
		router := NewRouter(cfg)

		malformedJSON := []byte(`{"invalid": json}`)
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		req := createAuthenticatedRequest("POST", "/v1/notifications", malformedJSON, roles.Api, svcID)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should handle malformed JSON gracefully
		assert.NotEqual(t, 0, w.Code)
	})

	t.Run("Router handles very long URLs", func(t *testing.T) {
		router := NewRouter(cfg)

		longPath := "/nonexistent/" + strings.Repeat("a", 1000)
		req := httptest.NewRequest("GET", longPath, nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Protected routes return 401 (Unauthorized) before 404 (Not Found) because auth middleware runs first
		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Router handles concurrent requests", func(t *testing.T) {
		router := NewRouter(cfg)

		const numRequests = 10
		results := make(chan int, numRequests)

		for i := 0; i < numRequests; i++ {
			go func() {
				req := httptest.NewRequest("GET", "/health-check", nil)
				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)
				results <- w.Code
			}()
		}

		// Collect all results
		for i := 0; i < numRequests; i++ {
			code := <-results
			assert.Equal(t, http.StatusOK, code)
		}
	})
}

func TestRouterIntegration(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("Complete mobile request flow", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")

		req := createAuthenticatedRequest("GET", "/mobile/ff", nil, roles.IOS, svcID)
		req.Header.Set("X-App-Version", "1.0.0")
		req.Header.Set("X-Service-Id", svcID.String())
		req.Header.Set("X-Device-Id", dvcID.String())
		req.Header.Set("X-App-Platform", "ios")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should pass all middleware checks
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
		assert.NotEqual(t, http.StatusForbidden, w.Code)
		assert.NotEqual(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Complete demo request flow", func(t *testing.T) {
		demoSvcID := appioid.MustParse("demo_svc_00000000000000000000000001")

		req := createAuthenticatedRequest("GET", "/demo-appio-so/services/"+demoSvcID.String(), nil, roles.DemoAppioSo, demoSvcID)
		req.Header.Set("X-Service-Id", demoSvcID.String())

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should pass all middleware checks
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
		assert.NotEqual(t, http.StatusForbidden, w.Code)
		assert.NotEqual(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Complete API request flow", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		req := createAuthenticatedRequest("GET", "/v1/services", nil, roles.Api, svcID)
		req.Header.Set("X-Service-Id", svcID.String())

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should pass all middleware checks
		assert.NotEqual(t, http.StatusUnauthorized, w.Code)
		assert.NotEqual(t, http.StatusForbidden, w.Code)
	})
}

func TestRouterMiddlewareOrder(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)

	t.Run("Global middlewares are applied in correct order", func(t *testing.T) {
		router := NewRouter(cfg)

		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Check that headers set by global middlewares are present
		// Note: health-check handler calls helpers.RenderJSON which overrides the global Content-Type
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	})

	t.Run("Route-specific middlewares override global ones when needed", func(t *testing.T) {
		router := NewRouter(cfg)

		// Test robots.txt which sets its own content type
		req := httptest.NewRequest("GET", "/robots.txt", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		// Should override the global JSON content type
		assert.Equal(t, "text/plain", w.Header().Get("Content-Type"))
		// But should still have the security header from global middleware
		assert.Equal(t, "nosniff", w.Header().Get("X-Content-Type-Options"))
	})
}

func TestMobileRouteLastSeenAtMiddleware(t *testing.T) {
	serviceContainer, ctrl := createTestServiceContainer(t)
	defer ctrl.Finish()

	cfg := createTestConfig(t, serviceContainer)
	router := NewRouter(cfg)

	t.Run("LastSeenAtMiddleware is applied to mobile routes", func(t *testing.T) {
		// Create a request to a mobile route with device ID
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		req := createAuthenticatedRequest("GET", "/mobile/services", nil, roles.IOS, svcID)
		req.Header.Set("X-Service-Id", svcID.String())
		req.Header.Set("X-Device-Id", dvcID.String())

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// The middleware should have been called (verified by mock expectations)
		// The request should continue to the handler
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("LastSeenAtMiddleware handles missing device ID gracefully", func(t *testing.T) {
		// Create a request to a mobile route without device ID
		svcID := appioid.MustParse("svc_00000000000000000000000001")

		req := createAuthenticatedRequest("GET", "/mobile/services", nil, roles.IOS, svcID)
		req.Header.Set("X-Service-Id", svcID.String())
		// No X-Device-Id header

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should still work without device ID
		assert.NotEqual(t, http.StatusInternalServerError, w.Code)
	})
}
